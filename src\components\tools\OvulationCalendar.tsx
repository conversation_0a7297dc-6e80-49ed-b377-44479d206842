'use client';

import { useState, useEffect } from 'react';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, isToday } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface OvulationCalendarProps {
  lastPeriodDate?: Date;
  ovulationDate?: Date;
  fertileWindowStart?: Date;
  fertileWindowEnd?: Date;
  nextPeriodDate?: Date;
  className?: string;
}

export function OvulationCalendar({
  lastPeriodDate,
  ovulationDate,
  fertileWindowStart,
  fertileWindowEnd,
  nextPeriodDate,
  className
}: OvulationCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Set the current month to show the relevant dates
  useEffect(() => {
    if (ovulationDate) {
      setCurrentMonth(startOfMonth(ovulationDate));
    } else if (fertileWindowStart) {
      setCurrentMonth(startOfMonth(fertileWindowStart));
    } else if (lastPeriodDate) {
      setCurrentMonth(startOfMonth(lastPeriodDate));
    }
  }, [lastPeriodDate, ovulationDate, fertileWindowStart]);

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(monthStart);
  const startDate = startOfWeek(monthStart, { weekStartsOn: 6 }); // Saturday
  const endDate = endOfWeek(monthEnd, { weekStartsOn: 6 });

  // Days of week headers (Arabic)
  const daysOfWeek = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];

  // Generate all days for the calendar
  const calendarDays = [];
  let day = startDate;
  while (day <= endDate) {
    calendarDays.push(day);
    day = addDays(day, 1);
  }

  const getDayType = (day: Date) => {
    if (lastPeriodDate && isSameDay(day, lastPeriodDate)) {
      return 'period-start';
    }
    if (ovulationDate && isSameDay(day, ovulationDate)) {
      return 'ovulation';
    }
    if (fertileWindowStart && fertileWindowEnd &&
        day >= fertileWindowStart && day <= fertileWindowEnd) {
      return 'fertile';
    }
    if (nextPeriodDate && isSameDay(day, nextPeriodDate)) {
      return 'next-period';
    }
    return 'normal';
  };

  const getDayClasses = (day: Date) => {
    const dayType = getDayType(day);
    const baseClasses = "w-full h-12 sm:h-14 md:h-16 flex items-center justify-center text-sm sm:text-base font-semibold rounded-lg transition-all duration-200 hover:scale-105 cursor-pointer";

    if (!isSameMonth(day, monthStart)) {
      return cn(baseClasses, "text-gray-300 hover:text-gray-400");
    }

    let todayClasses = "";
    if (isToday(day)) {
      todayClasses = "ring-2 ring-blue-500 ring-offset-1";
    }

    switch (dayType) {
      case 'period-start':
        return cn(baseClasses, "text-white shadow-md", todayClasses);
      case 'ovulation':
        return cn(baseClasses, "text-white shadow-md", todayClasses);
      case 'fertile':
        return cn(baseClasses, "text-white shadow-md", todayClasses);
      case 'next-period':
        return cn(baseClasses, "text-white shadow-md", todayClasses);
      default:
        return cn(baseClasses, "text-gray-700 hover:bg-gray-100 bg-white border border-gray-200", todayClasses);
    }
  };

  const getDayStyle = (day: Date) => {
    const dayType = getDayType(day);
    switch (dayType) {
      case 'period-start':
        return { backgroundColor: '#ef4444' }; // red-500
      case 'ovulation':
        return { backgroundColor: '#b91c1c' }; // red-700
      case 'fertile':
        return { backgroundColor: '#34d399' }; // emerald-400
      case 'next-period':
        return { backgroundColor: '#8b5cf6' }; // violet-500
      default:
        return {};
    }
  };



  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  // Create calendar weeks
  const weeks = [];
  for (let i = 0; i < calendarDays.length; i += 7) {
    const week = calendarDays.slice(i, i + 7);
    weeks.push(week);
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4 sm:p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={prevMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          
          <h2 className="text-lg sm:text-xl font-semibold text-center">
            {format(currentMonth, 'MMMM yyyy', { locale: arSA })}
          </h2>
          
          <Button
            variant="outline"
            size="sm"
            onClick={nextMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        {/* Days of week header */}
        <div className="grid grid-cols-7 gap-1 sm:gap-2 mb-4">
          {daysOfWeek.map((day) => (
            <div
              key={day}
              className="h-10 sm:h-12 flex items-center justify-center text-sm sm:text-base font-bold text-gray-700 bg-gray-100 rounded-lg border border-gray-200"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="space-y-2">
          {weeks.map((week, weekIndex) => (
            <div key={weekIndex} className="grid grid-cols-7 gap-2">
              {week.map((day) => (
                <div
                  key={day.toString()}
                  className={getDayClasses(day)}
                  style={getDayStyle(day)}
                >
                  <span>{format(day, 'd', { locale: arSA })}</span>
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Legend */}
        {(lastPeriodDate || ovulationDate || fertileWindowStart) && (
          <div className="mt-6 pt-4 border-t">
            <div className="flex flex-wrap justify-center gap-4 text-xs sm:text-sm">
              {lastPeriodDate && (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span>بداية الحيض</span>
                </div>
              )}
              {fertileWindowStart && (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-emerald-400 rounded"></div>
                  <span>فترة الخصوبة</span>
                </div>
              )}
              {ovulationDate && (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-700 rounded"></div>
                  <span>يوم الإباضة</span>
                </div>
              )}
              {nextPeriodDate && (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-violet-500 rounded"></div>
                  <span>الدورة القادمة</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
