






import { notFound } from 'next/navigation';
import Link from 'next/link';
import { toolCategories, Tool } from '@/lib/tools';
import { PageHeader } from '@/components/PageHeader';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { loadToolContent } from '@/lib/content/loader';
import type { Metadata, ResolvingMetadata } from 'next';
import { JsonLd } from '@/components/JsonLd';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

type Props = {
  params: { slug: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { slug } = params;
  const tool = toolCategories.flatMap(c => c.tools).find(t => t.slug === slug);

  if (!tool) {
    return {
      title: 'أداة غير موجودة',
    };
  }

  const content = await loadToolContent(slug);
  const currentYear = new Date().getFullYear();
  const description = content.seoDescription?.replace(/<[^>]*>?/gm, '').substring(0, 160) || tool.description;
  const enhancedDescription = `${description} - أداة مجانية ${currentYear}`;
  const previousImages = (await parent).openGraph?.images || [];
  const toolUrl = siteUrl ? `${siteUrl}/tools/${slug}` : `/tools/${slug}`;
  const category = toolCategories.find(cat => cat.tools.some(t => t.slug === slug));

  // Generate keywords based on tool name and category
  const keywords = [
    tool.name,
    category?.name || '',
    'أدوات عربية',
    'حاسبة مجانية',
    'أدوات مجانية',
    `أدوات ${currentYear}`,
    'Arabic tools',
    'free calculator',
    tool.name.replace(/حاسبة|محول|أداة/g, '').trim()
  ].filter(Boolean);

  return {
    title: `${tool.name} - أداة مجانية ${currentYear}`,
    description: enhancedDescription,
    keywords: keywords.join(', '),
    alternates: {
      canonical: `/tools/${slug}`,
    },
    openGraph: {
      title: `${tool.name} | أدوات بالعربي ${currentYear}`,
      description: enhancedDescription,
      url: toolUrl,
      images: [...previousImages],
      type: 'article',
      locale: 'ar_SA',
      siteName: 'أدوات بالعربي',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${tool.name} | أدوات بالعربي ${currentYear}`,
      description: enhancedDescription,
      images: [...previousImages],
      site: '@adawat_org',
      creator: '@adawat_org',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function ToolPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const allTools = toolCategories.flatMap(c => c.tools);
  const tool = allTools.find(t => t.slug === slug);
  const category = toolCategories.find(cat => cat.tools.some(t => t.slug === slug));

  if (!tool || !tool.component) {
    notFound();
  }

  const content = await loadToolContent(tool.slug);
  const enrichedTool = { ...tool, ...content };

  let toolProps = {};
  if (tool.getData) {
    const data = await tool.getData();
    toolProps = { initialData: data };
  }

  // --- Improved Related Tools Logic ---
  let relatedTools: Tool[] = [];
  const relatedSlugs = new Set<string>();

  // 1. Add manually specified related tools first
  if (enrichedTool.relatedSlugs) {
    enrichedTool.relatedSlugs.forEach(relatedSlug => {
      const relatedTool = allTools.find(t => t.slug === relatedSlug);
      if (relatedTool && relatedTool.component && !relatedSlugs.has(relatedSlug)) {
        relatedTools.push(relatedTool);
        relatedSlugs.add(relatedSlug);
      }
    });
  }

  // 2. Add other tools from the same category to fill up the list
  if (category && relatedTools.length < 6) { // Limit total suggestions
    const categoryTools = category.tools.filter(t => t.slug !== slug && t.component && !relatedSlugs.has(t.slug));
    const remainingTools = categoryTools.slice(0, 6 - relatedTools.length);
    relatedTools.push(...remainingTools);
  }
  // --- End of Improved Logic ---
  
  const ToolComponent = tool.component;

  const breadcrumbJsonLd: any = siteUrl && category ? {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      { '@type': 'ListItem', position: 1, name: 'الرئيسية', item: siteUrl },
      { '@type': 'ListItem', position: 2, name: category.name, item: `${siteUrl}/categories/${category.slug}` },
      { '@type': 'ListItem', position: 3, name: tool.name, item: `${siteUrl}/tools/${slug}` },
    ],
  } : null;

  const faqJsonLd: any = enrichedTool.faq && enrichedTool.faq.length > 0 ? {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: enrichedTool.faq.map((item: any) => ({
      '@type': 'Question',
      name: item.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: item.answer,
      },
    })),
  } : null;

  // Tool structured data
  const toolJsonLd: any = siteUrl ? {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: tool.name,
    description: tool.description,
    url: `${siteUrl}/tools/${slug}`,
    applicationCategory: 'UtilityApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock'
    },
    provider: {
      '@type': 'Organization',
      name: 'أدوات بالعربي',
      url: siteUrl
    },
    inLanguage: 'ar',
    isAccessibleForFree: true,
    keywords: [tool.name, category?.name, 'أدوات عربية', 'حاسبة مجانية'].filter(Boolean).join(', '),
    dateModified: new Date().toISOString(),
    datePublished: new Date().toISOString()
  } : null;

  const isWideTool = [
    'summarize-arabic-text',
    'paraphrase-text',
    'resignation-letter-generator',
    'financial-aid-request-generator',
    'ovulation-calculator'
  ].includes(slug);

  return (
    <div className="w-full flex flex-col items-center">
      {breadcrumbJsonLd && <JsonLd data={breadcrumbJsonLd} />}
      {faqJsonLd && <JsonLd data={faqJsonLd} />}
      {toolJsonLd && <JsonLd data={toolJsonLd} />}
      
      <div className={cn(
          "w-full flex justify-center",
          isWideTool ? "max-w-6xl" : "max-w-4xl"
      )}>
        <div className={cn(
          "w-full",
           isWideTool ? "max-w-full" : "max-w-2xl"
        )}>
            <ToolComponent {...toolProps} />
        </div>
      </div>
      
       <div className="w-full max-w-4xl mt-16 space-y-12">
            {relatedTools.length > 0 && (
              <section>
                <h2 className="text-2xl font-headline font-bold text-center mb-6">أدوات ذات صلة</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {relatedTools.map(relatedTool => (
                    <Link key={relatedTool.path} href={relatedTool.path} className="group">
                      <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {relatedTool.icon && (
                                        <div className="p-2 rounded-full bg-primary/10 text-primary">
                                            <relatedTool.icon className="w-5 h-5" />
                                        </div>
                                    )}
                                    <CardTitle className="font-headline text-lg">{relatedTool.name}</CardTitle>
                                </div>
                                <ArrowLeft className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary shrink-0" />
                            </div>
                        </CardHeader>
                         <CardContent>
                            <p className="text-sm text-muted-foreground">
                              {relatedTool.description}
                            </p>
                          </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </section>
            )}

            {enrichedTool.seoDescription && (
              <article>
                 <h2 className="text-2xl font-headline font-bold text-center mb-6">حول أداة {enrichedTool.name}</h2>
                 <div className="p-6 bg-card border rounded-lg space-y-4 text-base leading-relaxed text-card-foreground/90 prose-p:my-4 prose-ul:my-4 prose-ol:my-4" dangerouslySetInnerHTML={{ __html: enrichedTool.seoDescription }} />
              </article>
            )}
      
            {enrichedTool.faq && enrichedTool.faq.length > 0 && (
              <section>
                <h2 className="text-2xl font-headline font-bold text-center mb-6">أسئلة شائعة</h2>
                <Accordion type="single" collapsible className="w-full">
                  {enrichedTool.faq.map((item, index) => (
                    <AccordionItem value={`item-${index}`} key={index}>
                      <AccordionTrigger className="text-right text-lg">{item.question}</AccordionTrigger>
                      <AccordionContent className="text-base leading-relaxed">
                        <p>{item.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </section>
            )}
        </div>
    </div>
  );
}

export async function generateStaticParams() {
  const paths = toolCategories
    .flatMap(category => category.tools)
    .filter(tool => tool.component)
    .map(tool => ({
      slug: tool.slug,
    }));

  return paths;
}
