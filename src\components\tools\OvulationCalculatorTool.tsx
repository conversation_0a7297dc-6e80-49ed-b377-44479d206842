
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Target, Leaf, CircleHelp, Droplet, TestTube, Calendar as CalendarIcon } from 'lucide-react';
import { format, addDays, subDays, startOfMonth, addMonths, subMonths } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';


const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
  cycleLength: requiredNumber('طول الدورة مطلوب').int().min(20, "طول الدورة عادة بين 20-45 يومًا.").max(45, "طول الدورة عادة بين 20-45 يومًا.").default(28),
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  lastPeriodDate: Date;
  ovulationDate: Date;
  fertileWindowStart: Date;
  fertileWindowEnd: Date;
  nextPeriodDate: Date;
  testDay: Date;
}

const DateFields = ({ control }: { control: Control<FormValues> }) => {
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const days = Array.from({ length: 31 }, (_, i) => i + 1);

    return (
    <div className="space-y-3">
        <FormLabel className="text-base font-medium">أول يوم من آخر دورة شهرية</FormLabel>
        <div className="grid grid-cols-2 gap-4">
            <FormField
              control={control}
              name="day"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-muted-foreground">اليوم</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="اختر اليوم" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {days.map(d => <SelectItem key={d} value={String(d)}>{d}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="month"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-muted-foreground">الشهر</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="اختر الشهر" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {months.map(m => <SelectItem key={m} value={String(m)}>{m}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
        </div>
    </div>
)};

const ResultInfoCard = ({ date, title, colorClass, icon: Icon }: { date: Date, title: string, colorClass: string, icon: React.ElementType }) => (
    <div className={`text-center p-4 rounded-lg border-b-4 transition-all hover:shadow-md ${colorClass}`}>
        <p className="text-2xl font-bold">{format(date, 'd', { locale: arSA })}</p>
        <p className="text-sm text-muted-foreground">{format(date, 'MMMM yyyy', { locale: arSA })}</p>
        <div className="flex items-center justify-center gap-2 mt-3 text-sm font-medium">
            <Icon className="h-5 w-5" />
            <span>{title}</span>
        </div>
    </div>
);

export function OvulationCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);
  const [displayMonth, setDisplayMonth] = useState<Date>(new Date());

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      cycleLength: 28,
      day: undefined,
      month: undefined,
    },
  });

  function onSubmit(data: FormValues) {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    let year = currentYear;
    if (data.month > currentMonth) {
        year = currentYear - 1;
    }

    const lastPeriodDate = new Date(year, data.month - 1, data.day);
    const { cycleLength } = data;
    
    if (isNaN(lastPeriodDate.getTime()) || lastPeriodDate > now) {
        form.setError("day", { message: "تاريخ غير صالح أو في المستقبل." });
        return;
    }

    const ovulationDate = addDays(lastPeriodDate, cycleLength - 14);
    const fertileWindowStart = subDays(ovulationDate, 5);
    const fertileWindowEnd = ovulationDate; // The fertile window ends on ovulation day
    const nextPeriodDate = addDays(lastPeriodDate, cycleLength);
    const testDay = addDays(nextPeriodDate, 1);

    const newResult = {
      lastPeriodDate,
      ovulationDate,
      fertileWindowStart,
      fertileWindowEnd,
      nextPeriodDate,
      testDay
    };

    setResult(newResult);
    setDisplayMonth(startOfMonth(newResult.fertileWindowStart));
  }
  
  const modifiers = result ? {
    lastPeriod: result.lastPeriodDate,
    fertile: { from: result.fertileWindowStart, to: result.fertileWindowEnd },
    ovulation: result.ovulationDate,
  } : {};

  const modifiersStyles = {
    lastPeriod: {
      backgroundColor: '#fecaca', // red-200
      color: '#991b1b', // red-800
      borderRadius: '50%'
    },
    fertile: {
      backgroundColor: '#a7f3d0', // green-200
      color: '#065f46', // green-800
      borderRadius: '50%'
    },
    ovulation: {
      backgroundColor: '#ef4444', // red-500
      color: 'white',
      borderRadius: '50%'
    },
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة التبويض</CardTitle>
        <CardDescription>توقعي أيام الخصوبة لديك لزيادة فرص الحمل.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
               <div className="lg:col-span-2">
                 <DateFields control={form.control} />
               </div>
               <FormField name="cycleLength" control={form.control} render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className="text-base font-medium">متوسط طول الدورة (بالأيام)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        className="h-11 text-center text-lg font-medium"
                        placeholder="28"
                      />
                    </FormControl>
                    <FormMessage />
                    <p className="text-xs text-muted-foreground">عادة بين 20-45 يومًا</p>
                  </FormItem>
              )}/>
            </div>
            <Button type="submit" className="w-full h-12 text-lg font-medium">احسبي أيام التبويض</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8">
            <h3 className="text-xl font-headline font-semibold mb-6 text-center">النتائج التقديرية</h3>

            {/* Layout for wider screens */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
              {/* Results Cards */}
              <div className="space-y-6">
                <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
                   <ResultInfoCard date={result.lastPeriodDate} title="بداية الحيض" colorClass="bg-red-100 border-red-500" icon={Droplet}/>
                   <ResultInfoCard date={result.fertileWindowStart} title="بداية الخصوبة" colorClass="bg-green-100 border-green-500" icon={Leaf}/>
                   <ResultInfoCard date={result.ovulationDate} title="يوم الإباضة" colorClass="bg-red-100 border-red-500" icon={Target}/>
                   <ResultInfoCard date={result.fertileWindowEnd} title="نهاية الخصوبة" colorClass="bg-green-100 border-green-500" icon={Leaf}/>
                   <ResultInfoCard date={result.testDay} title="أفضل يوم للاختبار" colorClass="bg-blue-100 border-blue-500" icon={TestTube}/>
                   <ResultInfoCard date={result.nextPeriodDate} title="الدورة القادمة" colorClass="bg-gray-100 border-gray-500" icon={CircleHelp}/>
                </div>

                {/* Legend */}
                <div className="flex flex-wrap justify-center gap-x-6 gap-y-2 text-sm">
                    <div className="flex items-center gap-2"><div className="w-4 h-4 rounded-full bg-red-200 border border-red-400"></div><span>بداية الحيض</span></div>
                    <div className="flex items-center gap-2"><div className="w-4 h-4 rounded-full bg-green-200 border border-green-400"></div><span>فترة الخصوبة</span></div>
                    <div className="flex items-center gap-2"><div className="w-4 h-4 rounded-full bg-red-500 border border-red-600"></div><span>يوم الإباضة</span></div>
                </div>
              </div>

              {/* Calendar */}
              <div className="flex justify-center">
                <Card className="w-full max-w-md">
                    <CardContent className="p-4">
                        <Calendar
                            month={displayMonth}
                            onMonthChange={setDisplayMonth}
                            modifiers={modifiers}
                            modifiersStyles={modifiersStyles}
                            className="w-full"
                            locale={arSA}
                         />
                    </CardContent>
                </Card>
              </div>
            </div>

            <p className="text-xs text-muted-foreground mt-6 text-center">
                هذه النتائج هي مجرد تقديرات. تختلف دورة كل امرأة عن الأخرى.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
